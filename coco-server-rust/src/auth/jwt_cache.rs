use crate::auth::user_claims::UserClaims;
use dashmap::DashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::interval;
use tracing::{debug, info};

/// JWT验证缓存项
#[derive(Debug, Clone)]
struct CacheItem {
    /// 用户声明
    claims: UserClaims,
    /// 缓存创建时间
    created_at: Instant,
    /// 缓存过期时间
    expires_at: Instant,
}

impl CacheItem {
    /// 创建新的缓存项
    fn new(claims: UserClaims, ttl: Duration) -> Self {
        let now = Instant::now();
        Self {
            claims,
            created_at: now,
            expires_at: now + ttl,
        }
    }

    /// 检查缓存项是否过期
    fn is_expired(&self) -> bool {
        Instant::now() > self.expires_at
    }

    /// 检查JWT令牌本身是否过期
    fn is_jwt_expired(&self) -> bool {
        self.claims.is_expired()
    }
}

/// JWT验证缓存
#[derive(Debug)]
pub struct JwtCache {
    /// 缓存存储，使用令牌哈希作为键
    cache: Arc<DashMap<String, CacheItem>>,
    /// 缓存TTL（生存时间）
    ttl: Duration,
    /// 最大缓存大小
    max_size: usize,
}

impl JwtCache {
    /// 创建新的JWT缓存
    pub fn new(ttl: Duration, max_size: usize) -> Self {
        let cache = Arc::new(DashMap::new());

        // 启动后台清理任务
        let cache_clone = cache.clone();
        tokio::spawn(async move {
            Self::cleanup_task(cache_clone).await;
        });

        Self {
            cache,
            ttl,
            max_size,
        }
    }

    /// 获取缓存的JWT验证结果
    pub fn get(&self, token: &str) -> Option<UserClaims> {
        let token_hash = self.hash_token(token);

        if let Some(item) = self.cache.get(&token_hash) {
            // 检查缓存是否过期
            if item.is_expired() || item.is_jwt_expired() {
                debug!("JWT缓存项已过期，移除: {}", self.mask_token(token));
                drop(item); // 释放读锁
                self.cache.remove(&token_hash);
                return None;
            }

            debug!("JWT缓存命中: {}", self.mask_token(token));
            return Some(item.claims.clone());
        }

        debug!("JWT缓存未命中: {}", self.mask_token(token));
        None
    }

    /// 缓存JWT验证结果
    pub fn put(&self, token: &str, claims: UserClaims) {
        let token_hash = self.hash_token(token);

        // 检查缓存大小限制
        if self.cache.len() >= self.max_size {
            self.evict_oldest();
        }

        let cache_item = CacheItem::new(claims, self.ttl);
        self.cache.insert(token_hash, cache_item);

        debug!("JWT验证结果已缓存: {}", self.mask_token(token));
    }

    /// 移除特定令牌的缓存
    pub fn remove(&self, token: &str) {
        let token_hash = self.hash_token(token);
        if self.cache.remove(&token_hash).is_some() {
            debug!("JWT缓存项已移除: {}", self.mask_token(token));
        }
    }

    /// 清空所有缓存
    pub fn clear(&self) {
        let count = self.cache.len();
        self.cache.clear();
        info!("JWT缓存已清空，移除了 {} 个项目", count);
    }

    /// 获取缓存统计信息
    pub fn stats(&self) -> JwtCacheStats {
        let total_items = self.cache.len();
        let mut expired_items = 0;
        let now = Instant::now();

        for item in self.cache.iter() {
            if item.is_expired() || item.is_jwt_expired() {
                expired_items += 1;
            }
        }

        JwtCacheStats {
            total_items,
            expired_items,
            active_items: total_items - expired_items,
            max_size: self.max_size,
            ttl_seconds: self.ttl.as_secs(),
        }
    }

    /// 手动清理过期项
    pub fn cleanup_expired(&self) -> usize {
        let mut removed_count = 0;
        let mut to_remove = Vec::new();

        // 收集过期的键
        for entry in self.cache.iter() {
            if entry.is_expired() || entry.is_jwt_expired() {
                to_remove.push(entry.key().clone());
            }
        }

        // 移除过期项
        for key in to_remove {
            if self.cache.remove(&key).is_some() {
                removed_count += 1;
            }
        }

        if removed_count > 0 {
            debug!("清理了 {} 个过期的JWT缓存项", removed_count);
        }

        removed_count
    }

    /// 驱逐最旧的缓存项
    fn evict_oldest(&self) {
        let mut oldest_key: Option<String> = None;
        let mut oldest_time = Instant::now();

        // 找到最旧的项
        for entry in self.cache.iter() {
            if entry.created_at < oldest_time {
                oldest_time = entry.created_at;
                oldest_key = Some(entry.key().clone());
            }
        }

        // 移除最旧的项
        if let Some(key) = oldest_key {
            self.cache.remove(&key);
            debug!("驱逐最旧的JWT缓存项");
        }
    }

    /// 对令牌进行哈希处理以保护隐私
    fn hash_token(&self, token: &str) -> String {
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(token.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    /// 掩码令牌用于日志记录
    fn mask_token(&self, token: &str) -> String {
        if token.len() <= 8 {
            "*".repeat(token.len())
        } else {
            format!("{}...{}", &token[..4], &token[token.len() - 4..])
        }
    }

    /// 后台清理任务
    async fn cleanup_task(cache: Arc<DashMap<String, CacheItem>>) {
        let mut cleanup_interval = interval(Duration::from_secs(300)); // 每5分钟清理一次

        loop {
            cleanup_interval.tick().await;

            let mut removed_count = 0;
            let mut to_remove = Vec::new();

            // 收集过期的键
            for entry in cache.iter() {
                if entry.is_expired() || entry.is_jwt_expired() {
                    to_remove.push(entry.key().clone());
                }
            }

            // 移除过期项
            for key in to_remove {
                if cache.remove(&key).is_some() {
                    removed_count += 1;
                }
            }

            if removed_count > 0 {
                info!("后台清理任务移除了 {} 个过期的JWT缓存项", removed_count);
            }
        }
    }
}

/// JWT缓存统计信息
#[derive(Debug, Clone)]
pub struct JwtCacheStats {
    /// 总缓存项数
    pub total_items: usize,
    /// 过期项数
    pub expired_items: usize,
    /// 活跃项数
    pub active_items: usize,
    /// 最大缓存大小
    pub max_size: usize,
    /// TTL（秒）
    pub ttl_seconds: u64,
}

impl Default for JwtCache {
    fn default() -> Self {
        // 默认配置：TTL 5分钟，最大1000个项目
        Self::new(Duration::from_secs(300), 1000)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::auth::user_claims::UserClaims;
    use std::time::{SystemTime, UNIX_EPOCH};

    fn create_test_claims() -> UserClaims {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize;

        UserClaims::new(
            "test-user".to_string(),
            "testuser".to_string(),
            vec!["user".to_string()],
            "simple".to_string(),
            now + 3600, // 1小时后过期
            now,
        )
    }

    #[tokio::test]
    async fn test_jwt_cache_basic_operations() {
        let cache = JwtCache::new(Duration::from_secs(60), 100);
        let token = "test-jwt-token";
        let claims = create_test_claims();

        // 测试缓存未命中
        assert!(cache.get(token).is_none());

        // 测试缓存存储
        cache.put(token, claims.clone());

        // 测试缓存命中
        let cached_claims = cache.get(token).unwrap();
        assert_eq!(cached_claims.user_id, claims.user_id);
        assert_eq!(cached_claims.username, claims.username);

        // 测试缓存移除
        cache.remove(token);
        assert!(cache.get(token).is_none());
    }

    #[tokio::test]
    async fn test_jwt_cache_expiration() {
        let cache = JwtCache::new(Duration::from_millis(100), 100);
        let token = "test-jwt-token";
        let claims = create_test_claims();

        // 缓存项
        cache.put(token, claims);

        // 立即获取应该成功
        assert!(cache.get(token).is_some());

        // 等待过期
        tokio::time::sleep(Duration::from_millis(150)).await;

        // 过期后应该返回None
        assert!(cache.get(token).is_none());
    }

    #[tokio::test]
    async fn test_jwt_cache_size_limit() {
        let cache = JwtCache::new(Duration::from_secs(60), 2);

        // 添加3个项目，应该只保留2个
        cache.put("token1", create_test_claims());
        cache.put("token2", create_test_claims());
        cache.put("token3", create_test_claims());

        let stats = cache.stats();
        assert_eq!(stats.total_items, 2);
    }

    #[tokio::test]
    async fn test_jwt_cache_stats() {
        let cache = JwtCache::new(Duration::from_secs(60), 100);
        let claims = create_test_claims();

        cache.put("token1", claims.clone());
        cache.put("token2", claims);

        let stats = cache.stats();
        assert_eq!(stats.total_items, 2);
        assert_eq!(stats.max_size, 100);
        assert_eq!(stats.ttl_seconds, 60);
    }
}
